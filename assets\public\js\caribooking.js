/**
 * Caribooking (Check Booking) Functionality
 *
 * This file handles the booking search functionality for the caribooking page.
 */

$(document).ready(function() {
    console.log('Caribooking functionality initialized');

    // Initialize the booking search form
    initBookingSearchForm();
});

/**
 * Initialize booking search form
 */
function initBookingSearchForm() {
    const searchForm = document.getElementById('booking-search-form');
    const errorContainer = document.getElementById('error-message');
    const resultsContainer = document.getElementById('booking-results');

    if (!searchForm) {
        console.error('Booking search form not found');
        return;
    }

    // Add form submit handler
    searchForm.addEventListener('submit', function(event) {
        event.preventDefault();

        const searchInput = document.getElementById('search-input');
        const searchTerm = searchInput.value.trim();

        if (!searchTerm) {
            showError('<PERSON>lakan masukkan Kode Booking atau NIK');
            return;
        }

        // Hide previous results and errors
        hideError();
        hideResults();

        // Show loading state
        showLoading();

        // Perform search
        searchBooking(searchTerm);
    });
}

/**
 * Search for booking by code or NIK
 *
 * @param {string} searchTerm - Booking code or NIK to search for
 */
function searchBooking(searchTerm) {
    console.log('Searching for booking with term:', searchTerm);

    // Create URL with query parameters for GET request
    const url = new URL('registration/api/get_booking_by_nik.php', window.location.origin + window.location.pathname);
    url.searchParams.append('nik', searchTerm);

    // Make AJAX request using GET method
    fetch(url, {
        method: 'GET'
    })
    .then(response => response.json())
    .then(data => {
        console.log('Search response:', data);
        hideLoading();

        if (data.success) {
            if (data.is_multiple) {
                // Multiple bookings found
                displayMultipleBookings(data.data);
            } else {
                // Single booking found - data is always an array, so get the first item
                displaySingleBooking(data.data[0]);
            }
        } else {
            // No booking found
            showError(data.message || 'Booking tidak ditemukan. Silakan periksa kembali Kode Booking atau NIK yang dimasukkan.');
        }
    })
    .catch(error => {
        console.error('Error searching booking:', error);
        hideLoading();
        showError('Terjadi kesalahan saat mencari booking. Silakan coba lagi.');
    });
}

/**
 * Display single booking result
 *
 * @param {Object} booking - Booking data
 */
function displaySingleBooking(booking) {
    console.log('Displaying single booking:', booking);

    const bookingDetails = document.getElementById('booking-details');
    if (!bookingDetails) {
        console.error('Booking details container not found');
        return;
    }

    // Format the booking date
    const bookingDate = formatDate(booking.booking_date);

    // Create booking details HTML
    const detailsHTML = `
        <div class="modern-table">
            <table style="width: 100%;">
                <tr>
                    <td class="detail-label">Kode Booking</td>
                    <td class="detail-value"><strong>${booking.booking_number || '-'}</strong></td>
                </tr>
                <tr>
                    <td class="detail-label">Nama Pasien</td>
                    <td class="detail-value">${booking.patient_name || '-'}</td>
                </tr>
                <tr>
                    <td class="detail-label">NIK</td>
                    <td class="detail-value">${booking.nik || '-'}</td>
                </tr>
                <tr>
                    <td class="detail-label">No. Rekam Medis</td>
                    <td class="detail-value">${booking.medical_record_number || '-'}</td>
                </tr>
                <tr>
                    <td class="detail-label">Poli</td>
                    <td class="detail-value">${booking.poli_name || '-'}</td>
                </tr>
                <tr>
                    <td class="detail-label">Dokter</td>
                    <td class="detail-value">${booking.doctor_name || '-'}</td>
                </tr>
                <tr>
                    <td class="detail-label">Tanggal Kunjungan</td>
                    <td class="detail-value">${bookingDate}</td>
                </tr>
                <tr>
                    <td class="detail-label">Jam Kunjungan</td>
                    <td class="detail-value">${booking.booking_time || '-'}</td>
                </tr>
                <tr class="queue-row">
                    <td class="detail-label">No. Antrian</td>
                    <td class="detail-value"><span class="queue-number">${booking.queue_number || 'Belum tersedia'}</span></td>
                </tr>
                <tr>
                    <td class="detail-label">Status</td>
                    <td class="detail-value">
                        <span class="badge ${getStatusBadgeClass(booking.status)}">${getStatusText(booking.status)}</span>
                    </td>
                </tr>
            </table>
        </div>
    `;

    bookingDetails.innerHTML = detailsHTML;

    // Store booking data for printing
    window.currentBookingData = booking;

    // Show results
    showResults();
}

/**
 * Display multiple booking results
 *
 * @param {Array} bookings - Array of booking data
 */
function displayMultipleBookings(bookings) {
    console.log('Displaying multiple bookings:', bookings);

    const bookingDetails = document.getElementById('booking-details');
    if (!bookingDetails) {
        console.error('Booking details container not found');
        return;
    }

    let detailsHTML = `
        <div class="modern-alert modern-alert-info mb-4">
            <i class="fas fa-info-circle"></i>
            Ditemukan ${bookings.length} reservasi untuk NIK ini:
        </div>
    `;

    bookings.forEach((booking, index) => {
        const bookingDate = formatDate(booking.booking_date);

        detailsHTML += `
            <div class="modern-card mb-4">
                <div class="modern-card-header">
                    <h5>Reservasi ${index + 1}</h5>
                </div>
                <div class="modern-card-body">
                    <div class="modern-table">
                        <table style="width: 100%;">
                            <tr>
                                <td class="detail-label">Kode Booking</td>
                                <td class="detail-value"><strong>${booking.booking_number || '-'}</strong></td>
                            </tr>
                            <tr>
                                <td class="detail-label">Poli</td>
                                <td class="detail-value">${booking.poli_name || '-'}</td>
                            </tr>
                            <tr>
                                <td class="detail-label">Dokter</td>
                                <td class="detail-value">${booking.doctor_name || '-'}</td>
                            </tr>
                            <tr>
                                <td class="detail-label">Tanggal Kunjungan</td>
                                <td class="detail-value">${bookingDate}</td>
                            </tr>
                            <tr>
                                <td class="detail-label">Jam Kunjungan</td>
                                <td class="detail-value">${booking.booking_time || '-'}</td>
                            </tr>
                            <tr class="queue-row">
                                <td class="detail-label">No. Antrian</td>
                                <td class="detail-value"><span class="queue-number">${booking.queue_number || 'Belum tersedia'}</span></td>
                            </tr>
                            <tr>
                                <td class="detail-label">Status</td>
                                <td class="detail-value">
                                    <span class="badge ${getStatusBadgeClass(booking.status)}">${getStatusText(booking.status)}</span>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="text-center mt-4">
                        <button class="btn-modern btn-modern-outline btn-modern-sm" onclick="printSingleTicket(${JSON.stringify(booking).replace(/"/g, '&quot;')})">
                            <i class="fa fa-print" style="margin-right: 8px;"></i>Cetak Tiket
                        </button>
                    </div>
                </div>
            </div>
        `;
    });

    bookingDetails.innerHTML = detailsHTML;

    // Store booking data for printing (use the first/latest booking)
    window.currentBookingData = bookings[0];

    // Show results
    showResults();
}

/**
 * Print ticket for a single booking
 *
 * @param {Object} booking - Booking data to print
 */
function printSingleTicket(booking) {
    // Store the booking data temporarily
    const originalData = window.currentBookingData;
    window.currentBookingData = booking;

    // Call the print function
    printTicket();

    // Restore original data
    window.currentBookingData = originalData;
}

/**
 * Print ticket functionality
 */
function printTicket() {
    if (!window.currentBookingData) {
        showError('Data booking tidak tersedia untuk dicetak');
        return;
    }

    // Redirect to ticket page with booking data
    const bookingCode = window.currentBookingData.booking_number;
    if (bookingCode) {
        window.open(`registration/templates/ticket.php?booking_code=${bookingCode}`, '_blank');
    } else {
        showError('Kode booking tidak tersedia');
    }
}

/**
 * Format date to Indonesian format
 *
 * @param {string} dateString - Date string to format (DD-MM-YYYY format)
 * @returns {string} Formatted date
 */
function formatDate(dateString) {
    if (!dateString) return '-';

    try {
        // Handle DD-MM-YYYY format from API
        if (dateString.includes('-') && dateString.length === 10) {
            const parts = dateString.split('-');
            if (parts.length === 3) {
                const day = parseInt(parts[0]);
                const month = parseInt(parts[1]) - 1; // Month is 0-indexed
                const year = parseInt(parts[2]);

                const date = new Date(year, month, day);

                const months = [
                    'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
                    'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
                ];

                const formattedDay = day.toString().padStart(2, '0');
                const monthName = months[month];

                return `${formattedDay} ${monthName}, ${year}`;
            }
        }

        // Fallback to standard date parsing
        const date = new Date(dateString);
        if (isNaN(date.getTime())) {
            return dateString; // Return original if parsing fails
        }

        const months = [
            'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
            'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
        ];

        const day = date.getDate().toString().padStart(2, '0');
        const month = months[date.getMonth()];
        const year = date.getFullYear();

        return `${day} ${month}, ${year}`;
    } catch (error) {
        console.error('Error formatting date:', error);
        return dateString;
    }
}

/**
 * Get status badge CSS class
 *
 * @param {string} status - Booking status
 * @returns {string} CSS class for status badge
 */
function getStatusBadgeClass(status) {
    switch (status) {
        case 'confirmed':
        case 'active':
            return 'badge-success';
        case 'pending':
            return 'badge-warning';
        case 'cancelled':
            return 'badge-danger';
        default:
            return 'badge-secondary';
    }
}

/**
 * Get status text
 *
 * @param {string} status - Booking status
 * @returns {string} Human-readable status text
 */
function getStatusText(status) {
    switch (status) {
        case 'confirmed':
            return 'Dikonfirmasi';
        case 'active':
            return 'Aktif';
        case 'pending':
            return 'Menunggu';
        case 'cancelled':
            return 'Dibatalkan';
        default:
            return status || 'Tidak diketahui';
    }
}

/**
 * Show error message
 *
 * @param {string} message - Error message to display
 */
function showError(message) {
    const errorContainer = document.getElementById('error-message');
    if (errorContainer) {
        errorContainer.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${message}`;
        errorContainer.style.display = 'block';
    }
}

/**
 * Hide error message
 */
function hideError() {
    const errorContainer = document.getElementById('error-message');
    if (errorContainer) {
        errorContainer.style.display = 'none';
    }
}

/**
 * Show results container
 */
function showResults() {
    const resultsContainer = document.getElementById('booking-results');
    if (resultsContainer) {
        resultsContainer.style.display = 'block';
    }
}

/**
 * Hide results container
 */
function hideResults() {
    const resultsContainer = document.getElementById('booking-results');
    if (resultsContainer) {
        resultsContainer.style.display = 'none';
    }
}

/**
 * Show loading state
 */
function showLoading() {
    const submitButton = document.querySelector('#booking-search-form button[type="submit"]');
    if (submitButton) {
        submitButton.innerHTML = '<i class="fas fa-spinner fa-spin" style="margin-right: 8px;"></i>Mencari...';
        submitButton.disabled = true;
    }
}

/**
 * Hide loading state
 */
function hideLoading() {
    const submitButton = document.querySelector('#booking-search-form button[type="submit"]');
    if (submitButton) {
        submitButton.innerHTML = '<i class="fa fa-search" style="margin-right: 8px;"></i>Cari Reservasi';
        submitButton.disabled = false;
    }
}
