<?php
/**
 * Booking Functions
 *
 * This file contains functions for handling bookings.
 */

// Include required dependencies
require_once __DIR__ . '/../../config/constants.php';
require_once __DIR__ . '/../../includes/functions.php';
require_once __DIR__ . '/../../includes/validation.php';

// Include API functions for external data fetching
require_once __DIR__ . '/../../includes/api_functions.php';


/**
 * Normalize API registration data to match local database structure
 *
 * @param  array $apiData Raw API response data
 * @return array Normalized booking data
 */
function normalizeApiRegistrationData($apiData)
{
    $normalizedBookings = [];

    // Check if API data has the expected structure
    if (!isset($apiData['data']) || !is_array($apiData['data'])) {
        return $normalizedBookings;
    }

    // Handle different possible API response structures
    $registrations = [];
    if (isset($apiData['data']['_resources']) && is_array($apiData['data']['_resources'])) {
        // Multiple registrations in _resources array
        $registrations = $apiData['data']['_resources'];
    } elseif (isset($apiData['data']['data']['_resources'])) {
        // Single registration object in data.data._resources
        $registrations = [$apiData['data']['data']['_resources']];
    } elseif (is_array($apiData['data'])) {
        $registrations = $apiData['data'];
    }

    foreach ($registrations as $registration) {
        // Map API fields to local database field names
        $normalizedBooking = [
            'id' => $registration['id'] ?? null,
            'booking_number' => $registration['registration_number'] ?? '',
            'patient_name' => $registration['patient']['person']['name'] ?? '',
            'nik' => $registration['patient']['person']['nik'] ?? '',
            'medical_record_number' => $registration['patient']['medical_record_number'] ?? '',
            'doctor_name' => $registration['doctor']['name'] ?? '',
            'poli_name' => $registration['clinic']['name'] ?? '',
            'booking_date' => $registration['registration_date'] ?? '',
            'booking_time' => $registration['schedule_start_time'] ?? '',
            'queue_number' => $registration['queue_number'] ?? '',
            'status' => (!empty($registration['is_canceled']) && $registration['is_canceled'] !== '0') ? 'canceled' : 'confirmed',
            'notes' => $registration['notes'] ?? '',
            'created_at' => $registration['created_at'] ?? '',
            'updated_at' => $registration['updated_at'] ?? ''
        ];



        $normalizedBookings[] = $normalizedBooking;
    }

    return $normalizedBookings;
}

/**
 * Get booking by NIK using API only
 *
 * @param  string $nik Patient's NIK
 * @return array|null Booking details if exists, null otherwise
 */
function getBookingByNIK($nik)
{

    // Validate NIK parameter
    if (empty($nik)) {
        error_log("getBookingByNIK: Empty NIK provided");
        return null;
    }

    // Trim and validate NIK
    $nik = trim($nik);
    if (strlen($nik) < 10) {
        error_log("getBookingByNIK: Invalid NIK format - " . $nik);
        return null;
    }

    // Fetch patient registrations from API
    $apiResult = fetchPatientRegistrationsByNIK($nik);




    // Check if API call was successful
    if (!$apiResult['success']) {

        return null;
    }

    // Check if we have data
    if (empty($apiResult['data'])) {

        return null;
    }

    // Normalize the API data using existing function
    $normalizedBookings = normalizeApiRegistrationData($apiResult);

    // Check if we have any bookings after normalization
    if (empty($normalizedBookings)) {

        return null;
    }

    // Determine if we have multiple bookings
    $isMultiple = count($normalizedBookings) > 1;

    // Return the result in a consistent format
    $result = [
        'is_multiple' => $isMultiple,
        'bookings' => $normalizedBookings,
        'count' => count($normalizedBookings)
    ];



    return $result;
}

/**
 * Get single booking by NIK and booking number (for ticket display)
 *
 * @param  string $nik           Patient's NIK
 * @param  string $bookingNumber Optional booking number to get specific booking
 * @return array|null Single booking details if exists, null otherwise
 */
function getSingleBookingByNIK($nik, $bookingNumber = null)
{
    // Get all bookings for the NIK
    $bookingsResult = getBookingByNIK($nik);

    if (!$bookingsResult || empty($bookingsResult['bookings'])) {
        return null;
    }

    $bookings = $bookingsResult['bookings'];

    // If booking number is specified, find that specific booking
    if (!empty($bookingNumber)) {
        foreach ($bookings as $booking) {
            if ($booking['booking_number'] === $bookingNumber) {
                return $booking;
            }
        }
        return null; // Booking number not found
    }

    // If no booking number specified, return the most recent booking
    // Assuming bookings are ordered by date, get the first one
    return $bookings[0];
}

/**
 * Process booking form
 *
 * @return array Result of processing
 */
function processBookingForm()
{
    // Check if form is submitted
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        return [
            'success' => false,
            'message' => 'Form not submitted',
            'data' => null
        ];
    }

    // Get form data
    $data = [
        'poli_id' => isset($_POST['poli_id']) ? (int)$_POST['poli_id'] : 0,
        'doctor_id' => isset($_POST['doctor_id']) ? (int)$_POST['doctor_id'] : 0,
        'booking_date' => isset($_POST['booking_date']) ? trim($_POST['booking_date']) : '',
        'booking_time' => isset($_POST['booking_time']) ? trim($_POST['booking_time']) : '',
        'notes' => isset($_POST['notes']) ? trim($_POST['notes']) : ''
    ];

    // Validate required fields
    $requiredFields = ['poli_id', 'doctor_id', 'booking_date', 'booking_time'];
    $missingFields = validateRequiredFields($data, $requiredFields);

    if (!empty($missingFields)) {
        return [
            'success' => false,
            'message' => ERROR_REQUIRED_FIELDS,
            'data' => null,
            'missing_fields' => $missingFields
        ];
    }

    // Validate date
    if (!validateDate($data['booking_date'])) {
        return [
            'success' => false,
            'message' => 'Tanggal tidak valid',
            'data' => null
        ];
    }

    // Validate time
    if (!validateTime($data['booking_time'])) {
        return [
            'success' => false,
            'message' => 'Waktu tidak valid',
            'data' => null
        ];
    }

    // Create booking
    $patientId = $_SESSION['patient_data']['id'];
    $result = createBooking(
        $patientId,
        $data['poli_id'],
        $data['doctor_id'],
        $data['booking_date'],
        $data['booking_time'],
        $data['notes']
    );

    // If successful, store booking data in session and move to ticket step
    if ($result['success']) {
        $_SESSION['booking_data'] = $result['data'];
        setCurrentStep(STEP_TICKET);
    }

    return $result;
}
