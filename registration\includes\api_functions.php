<?php
/**
 * API Functions
 *
 * This file contains functions for making API calls to external services.
 * These functions use cURL to fetch data from the SimKlinik API.
 */

/**
 * Get API token from configuration
 *
 * @return string API token
 */
function getApiToken()
{
    // In a production environment, this should be stored in a secure configuration file
    // or retrieved from environment variables
    return 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJwYXJ0bmVyX2lkIjozLCJjcmVhdGVkX2F0IjoiMjAyNS0wNS0xMCAwMTo1NTozMiJ9.zpfL3tWoPb-scBWkEPySxMyt_hw_C50ylzSYdT18v5c'; // Replace with actual token
}

/**
 * Make an API request using cURL
 *
 * @param  string $url     The API endpoint URL
 * @param  string $method  HTTP method (GET, POST, etc.)
 * @param  array  $data    Optional data to send with the request
 * @param  array  $headers Optional additional headers
 * @return array Associative array with 'success', 'data', and 'error' keys
 */
function makeApiRequest($url, $method = 'GET', $data = [], $headers = [])
{
    // Initialize cURL session
    $curl = curl_init();
    
    // Get API token from configuration
    $apiToken = getApiToken();

    // Set default headers
    $defaultHeaders = [
        'Authorization: Bearer ' . $apiToken,
        'Content-Type: application/json',
        'Accept: application/json'
    ];

    // Merge default headers with custom headers
    $finalHeaders = array_merge($defaultHeaders, $headers);

    // Set cURL options
    $curlOptions = [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => $method,
        CURLOPT_HTTPHEADER => $finalHeaders,
        CURLOPT_SSL_VERIFYPEER => true,
    ];

    // Add data to request if provided
    if (!empty($data)) {
        if ($method === 'GET') {
            // For GET requests, add data as query parameters
            $curlOptions[CURLOPT_URL] = $url . '?' . http_build_query($data);
        } else {
            // For other methods, add data as JSON in the request body
            $curlOptions[CURLOPT_POSTFIELDS] = json_encode($data);
        }
    }

    // Set all cURL options
    curl_setopt_array($curl, $curlOptions);

    // Execute the request
    $response = curl_exec($curl); 
   
    $err = curl_error($curl);
    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

    // Log detailed info for debugging
    error_log("makeApiRequest - URL: $url");
    error_log("makeApiRequest - Method: $method");
    error_log("makeApiRequest - HTTP Code: $httpCode");
    if ($err) {
        error_log("makeApiRequest - cURL Error: $err");
    }
    if ($httpCode >= 400) {
        error_log("makeApiRequest - Error Response: " . substr($response, 0, 1000));
    }

    // Close cURL session
    curl_close($curl);

    // Check for errors
    if ($err) {
        return [
            'success' => false,
            'data' => null,
            'error' => "cURL Error: $err"
        ];
    }

    // Parse JSON response
    $responseData = json_decode($response, true);
    // Check if JSON parsing was successful
    if (json_last_error() !== JSON_ERROR_NONE) {
        return [
            'success' => false,
            'data' => $response, // Return raw response for debugging
            'error' => 'Failed to parse JSON response: ' . json_last_error_msg()
        ];
    }

    // Check HTTP status code
    if ($httpCode >= 200 && $httpCode < 300) {
        return [
            'success' => true,
            'data' => $responseData,
            'error' => null
        ];
    } else {
        return [
            'success' => false,
            'data' => $responseData,
            'error' => "HTTP Error: $httpCode"
        ];
    }
}

/**
 * Fetch clinic data
 *
 * @param  int $size The number of records to retrieve (default: 100)
 * @return array Associative array with 'success', 'data', and 'error' keys
 */
function fetchClinics($size = 100)
{
    $url = 'https://staging.simklinik.com/api/integration/clinics';
    $params = [
        'order_by' => 'ordering',
        'size' => $size
    ];

    return makeApiRequest($url, 'GET', $params);
}

/**
 * Fetch doctors by clinic category
 *
 * @param  int $clinicCategoryId The clinic category ID
 * @param  int $page             The page number for pagination (default: 1)
 * @param  int $size             The number of records to retrieve (default: 100)
 * @return array Associative array with 'success', 'data', and 'error' keys
 */
function fetchDoctorsByClinic($clinicCategoryId, $page = 1, $size = 100)
{
    $url = "https://staging.simklinik.com/api/integration/clinic-category/{$clinicCategoryId}/doctors";
    $params = [
        'page' => $page,
        'size' => $size
    ];

    return makeApiRequest($url, 'GET', $params);
}

/**
 * Fetch education data
 *
 * @return array Associative array with 'success', 'data', and 'error' keys
 */
function fetchEducations()
{
    $url = 'https://staging.simklinik.com/api/integration/educations';

    return makeApiRequest($url);
}

/**
 * Fetch gender data
 *
 * @return array Associative array with 'success', 'data', and 'error' keys
 */
function fetchGenders()
{
    $url = 'https://staging.simklinik.com/api/integration/genders';

    return makeApiRequest($url);
}

/**
 * Fetch marital status data
 *
 * @return array Associative array with 'success', 'data', and 'error' keys
 */
function fetchMaritals()
{
    $url = 'https://staging.simklinik.com/api/integration/maritals';

    return makeApiRequest($url);
}

/**
 * Fetch profession data
 *
 * @return array Associative array with 'success', 'data', and 'error' keys
 */
function fetchProfessions()
{
    $url = 'https://staging.simklinik.com/api/integration/professions';

    return makeApiRequest($url);
}

/**
 * Fetch religion data
 *
 * @return array Associative array with 'success', 'data', and 'error' keys
 */
function fetchReligions()
{
    $url = 'https://staging.simklinik.com/api/integration/religions';

    return makeApiRequest($url);
}

/**
 * Fetch person data by NIK
 *
 * @param  string $nik The NIK (National Identity Number)
 * @return array Associative array with 'success', 'data', and 'error' keys
 */
function fetchPersonsByNIK($nik)
{
    $url = 'https://staging.simklinik.com/api/integration/persons';
    $params = [
        'nik' => $nik
    ];

    return makeApiRequest($url, 'GET', $params);
}

/**
 * Fetch villages by keyword
 *
 * @param  string $keyword The search keyword
 * @return array Associative array with 'success', 'data', and 'error' keys
 */
function fetchVillagesByKeyword($keyword)
{
    $url = 'https://staging.simklinik.com/api/integration/villages';
    $params = [
        'keyword' => $keyword
    ];

    $result = makeApiRequest($url, 'GET', $params);

    // Log the result for debugging
    error_log("Village search result: " . print_r($result, true));

    return $result;
}

/**
 * Create a new person (patient) in the SimKlinik system
 *
 * @param  array $personData The person data to create
 * @return array Associative array with 'success', 'data', and 'error' keys
 */
function createPerson($personData)
{
    $url = 'https://staging.simklinik.com/api/integration/person';
    
    return makeApiRequest($url, 'POST', $personData);
}

/**
 * Fetch registration types
 *
 * @param  int $size The number of records to retrieve (default: 100)
 * @return array Associative array with 'success', 'data', and 'error' keys
 */
function fetchRegistrationTypes($size = 100)
{
    $url = 'https://staging.simklinik.com/api/integration/registration-types';
    $params = [
        'size' => $size
    ];

    return makeApiRequest($url, 'GET', $params);
}

/**
 * Fetch clinic schedules
 *
 * @param int    $clinicId The clinic ID
 * @param string $date     Optional date filter (YYYY-MM-DD format)
 *
 * @return array Associative array with 'success', 'data', and 'error' keys
 */
function fetchClinicSchedules($clinicId, $date = null)
{
    $baseUrl = 'https://staging.simklinik.com/api/integration/clinic/';
    $url = $baseUrl . $clinicId . '/schedules';
    $params = [];

    // Add date parameter if provided
    if ($date) {
        $params['date'] = $date;
    }

    return makeApiRequest($url, 'GET', $params);
}

/**
 * Create patient registration (booking) in the SimKlinik system
 *
 * @param  array $registrationData The registration data to create
 * @return array Associative array with 'success', 'data', and 'error' keys
 */
function createPatientRegistration($registrationData)
{
    $url = 'https://staging.simklinik.com/api/integration/patient/registration';

    return makeApiRequest($url, 'POST', $registrationData);
}

/**
 * Fetch patient registrations (bookings) by NIK
 *
 * @param  string $nik The patient's NIK (National Identity Number)
 * @return array Associative array with 'success', 'data', and 'error' keys
 */
function fetchPatientRegistrationsByNIK($nik)
{

    // Validate NIK parameter
    if (empty($nik)) {
        return [
            'success' => false,
            'data' => null,
            'error' => 'NIK parameter is required'
        ];
    }

    // Trim and validate NIK format (basic validation)
    $nik = trim($nik);
    if (strlen($nik) < 10) {
        return [
            'success' => false,
            'data' => null,
            'error' => 'Invalid NIK format'
        ];
    }

    // Build the API URL with NIK parameter
    $url = "https://staging.simklinik.com/api/integration/patient/{$nik}/registration";

    // Make the API request
    $result = makeApiRequest($url, 'GET');

    // Log the API call for debugging
    error_log("Fetching patient registrations for NIK: {$nik}");
    error_log("API Response: " . print_r($result, true));

    return $result;
}
