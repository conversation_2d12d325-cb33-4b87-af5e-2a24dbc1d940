<?php
/**
 * Create Booking API
 *
 * This API creates a booking and returns the result.
 */

// Start session for compatibility
session_start();

// Include configuration files 
require_once '../config/constants.php';

// Include utility functions
require_once '../includes/functions.php';
require_once '../includes/validation.php';
require_once '../includes/cookie_functions.php';
require_once '../includes/api_functions.php'; 

// Set content type to JSON
header('Content-Type: application/json');

// Check if request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(
        [
        'success' => false,
        'message' => 'Invalid request method'
        ]
    );
    exit;
}

// Get NIK from request
$nik = isset($_POST['nik']) ? trim($_POST['nik']) : '';

// If NIK is not provided, check session as fallback
if (empty($nik) && isset($_SESSION['nik'])) {
    $nik = $_SESSION['nik'];
}

// If still no NIK, return error
if (empty($nik)) {
    echo json_encode(
        [
        'success' => false,
        'message' => 'NIK not provided'
        ]
    );
    exit;
}

// Variable to store patient data
$patientData = null;

// Try to get patient data from cookie
$patientData = getPatientDataFromCookie($nik);

if ($patientData) {
    error_log("Patient data retrieved from cookie for NIK: $nik in create_booking.php");
} else {
    error_log("No patient data found in cookie for NIK: $nik in create_booking.php");

    // If data not found in cookie, fall back to API
    include_once '../modules/nik_verification/functions.php';

    // Verify NIK to get patient data
    $patientResult = verifyNIK($nik);

    // Check if patient exists
    if (!$patientResult['success'] || $patientResult['patient_type'] !== PATIENT_EXISTING) {
        echo json_encode(
            [
            'success' => false,
            'message' => 'Patient data not found for the provided NIK'
            ]
        );
        exit;
    }

    // Store patient data
    $patientData = $patientResult['data'];

    // Store in cookie with expiration (e.g., 1 hour = 3600 seconds)
    storePatientDataInCookie($nik, $patientData);
    error_log("Patient data stored in cookie for NIK: $nik in create_booking.php");
}

// Get booking data from request
$data = [
    'poli_id' => isset($_POST['poli_id']) ? intval($_POST['poli_id']) : 0,
    'doctor_id' => isset($_POST['doctor_id']) ? intval($_POST['doctor_id']) : 0,
    'booking_date' => isset($_POST['booking_date']) ? trim($_POST['booking_date']) : '',
    'clinic_schedule_id' => isset($_POST['booking_time']) ? trim($_POST['booking_time']) : 0,
    'registration_type_id' => isset($_POST['registration_type_id']) ? intval($_POST['registration_type_id']) :
                             (isset($_POST['registration_type']) ? intval($_POST['registration_type']) : 1),
    'notes' => isset($_POST['notes']) ? trim($_POST['notes']) : ''
];

// Validate required fields
if (empty($data['poli_id']) || empty($data['doctor_id']) || empty($data['booking_date']) || empty($data['clinic_schedule_id']) || empty($data['registration_type_id'])) {
    echo json_encode(
        [
        'success' => false,
        'message' => 'Missing required fields: clinic, doctor, booking date, or clinic schedule'
        ]
    );
    exit;
}

// Get patient ID from session or patient data
$patientId = null;
if (isset($_SESSION['patient_data']['id'])) {
    $patientId = $_SESSION['patient_data']['id'];
} elseif (isset($patientData['id'])) {
    $patientId = $patientData['id'];
}

if (empty($patientId)) {
    echo json_encode(
        [
        'success' => false,
        'message' => 'Patient ID not found'
        ]
    );
    exit;
}

// Prepare registration data for the new API
$registrationData = [
    'clinic' => [
        'id' => $data['poli_id']
    ],
    'registration_type' => [
        'id' => $data['registration_type_id']
    ],
    'doctor' => [
        'id' => $data['doctor_id']
    ],
    'visit_type' => [
        'id' => 1  // Default visit type
    ],
    'patient' => [
        'id' => $patientId
    ],
    'registration_status' => [
        'id' => 1  // Default status for new registrations
    ],
    'referral' => [
        'id' => 3  // Default referral
    ],
    'clinic_schedule' => [
        'id' => $data['clinic_schedule_id']
    ],
    'branch' => [
        'id' => 1  // Default branch
    ],
    'registration_date' => date('Y-m-d'),
    'is_canceled' => false
];

// Log the registration data for debugging
error_log("Patient registration data being sent: " . print_r($registrationData, true));
error_log("Patient ID being used: " . $patientId);
error_log("Form data received: " . print_r($data, true));
// Create registration using the new API
$result = createPatientRegistration($registrationData);
print('<pre>'.print_r( $result ,true ).'</pre>');
die;

// Log the API response for debugging
error_log("Patient registration API response: " . print_r($result, true));
if (!$result['success']) {
    error_log("API Error Details - Error: " . ($result['error'] ?? 'Unknown'));
    error_log("API Error Details - Data: " . print_r($result['data'] ?? 'No data', true));
}

// If successful, handle the response
if ($result['success']) {
    // The new API might return different data structure
    // Adapt the response to match what the frontend expects
    $responseData = [
        'success' => true,
        'message' => 'Booking created successfully',
        'data' => $result['data']
    ];

    // Store booking data in cookie if we have an ID
    if (isset($result['data']['id'])) {
        // Enhance the booking data with additional information for ticket generation
        $enhancedBookingData = $result['data'];

        // Add patient information if available
        if (isset($_SESSION['patient_data'])) {
            $enhancedBookingData['patient_name'] = $_SESSION['patient_data']['name'] ?? '';
            $enhancedBookingData['medical_record_number'] = $_SESSION['patient_data']['medical_record_number'] ?? '';
        } elseif (isset($patientData)) {
            $enhancedBookingData['patient_name'] = $patientData['name'] ?? '';
            $enhancedBookingData['medical_record_number'] = $patientData['medical_record_number'] ?? '';
        }

        // Add booking details from form data
        $enhancedBookingData['booking_date'] = $data['booking_date'];
        $enhancedBookingData['booking_time'] = $data['booking_time'];
        $enhancedBookingData['poli_id'] = $data['poli_id'];
        $enhancedBookingData['doctor_id'] = $data['doctor_id'];

        // Try to get clinic and doctor names from APIs
        $clinicsResult = fetchClinics();
        if ($clinicsResult['success'] && isset($clinicsResult['data']['_resources'])) {
            foreach ($clinicsResult['data']['_resources'] as $clinic) {
                if ($clinic['id'] == $data['poli_id']) {
                    $enhancedBookingData['poli_name'] = $clinic['name'];
                    break;
                }
            }
        }

        $doctorsResult = fetchDoctorsByClinic($data['poli_id']);
        if ($doctorsResult['success'] && isset($doctorsResult['data']['_resources'])) {
            foreach ($doctorsResult['data']['_resources'] as $doctor) {
                if ($doctor['id'] == $data['doctor_id']) {
                    $enhancedBookingData['doctor_name'] = $doctor['name'];
                    break;
                }
            }
        }

        // Generate a booking number if not provided by API
        if (!isset($enhancedBookingData['booking_number'])) {
            include_once '../includes/functions.php';
            $enhancedBookingData['booking_number'] = generateBookingNumber();
        }

        storeBookingDataInCookie($result['data']['id'], $enhancedBookingData);
        error_log("Enhanced booking data stored in cookie with ID: " . $result['data']['id']);

        // Generate ticket with enhanced data
        include_once '../modules/ticket/functions.php';
        $ticketResult = generateTicketFromApiData($enhancedBookingData);

        if ($ticketResult['success']) {
            // Store ticket data in cookie
            storeTicketDataInCookie($ticketResult['data']['id'], $ticketResult['data']);
            error_log("Ticket data stored in cookie with ID: " . $ticketResult['data']['id']);
        }
    }

    echo json_encode($responseData);
} else {
    // Get detailed error message from API response
    $errorMessage = $result['error'] ?? 'Failed to create booking';

    // If we have API response data with error details, include them
    if (isset($result['data']) && is_array($result['data'])) {
        if (isset($result['data']['message'])) {
            $apiMessage = $result['data']['message'];

            // Handle known API validation bugs
            if (strpos($apiMessage, 'Tangal Kunjungan harus sebelum sekarang') !== false) {
                $errorMessage = 'Terjadi kesalahan validasi tanggal pada sistem. Silakan coba lagi atau hubungi administrator.';
            } elseif (strpos($apiMessage, 'Tangal Kunjungan harus berformat yyyy-mm-dd hh:mm:ss') !== false) {
                $errorMessage = 'Format tanggal tidak valid. Silakan pilih tanggal dan waktu yang benar.';
            } else {
                $errorMessage = $apiMessage;
            }
        } elseif (isset($result['data']['error'])) {
            $errorMessage = $result['data']['error'];
        } elseif (isset($result['data']['errors'])) {
            // Handle validation errors
            if (is_array($result['data']['errors'])) {
                $errors = array_values($result['data']['errors']);
                $errorMessage = 'Validation errors: ' . implode(', ', $errors);
            } else {
                $errorMessage = $result['data']['errors'];
            }
        }
    }

    // Return error response with detailed information
    $errorResponse = [
        'success' => false,
        'message' => $errorMessage,
        'data' => $result['data'] ?? null,
        'debug_info' => [
            'api_error' => $result['error'] ?? null,
            'api_response' => $result['data'] ?? null,
            'sent_data' => $registrationData
        ]
    ];

    echo json_encode($errorResponse);
}
