<?php
/**
 * Save Patient Data API
 *
 * This API saves patient data and returns the result.
 */

// Start session for compatibility
session_start();

// Include configuration files
require_once '../config/constants.php';

// Include utility functions
require_once '../includes/functions.php';
require_once '../includes/validation.php';
require_once '../includes/cookie_functions.php';
require_once '../includes/api_functions.php';

// Set content type to JSON
header('Content-Type: application/json');

// Check if request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(
        [
        'success' => false,
        'message' => 'Invalid request method'
        ]
    );
    exit;
}

// Get input data - support both JSON and form POST
$inputData = [];
$contentType = $_SERVER['CONTENT_TYPE'] ?? '';

if (strpos($contentType, 'application/json') !== false) {
    // Handle JSON input
    $jsonInput = file_get_contents('php://input');
    $inputData = json_decode($jsonInput, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        echo json_encode(
            [
            'success' => false,
            'message' => 'Invalid JSON input: ' . json_last_error_msg()
            ]
        );
        exit;
    }
} else {
    // Handle form POST input
    $inputData = $_POST;
}

// Get NIK from request first, then check session as fallback
$nik = isset($inputData['nik']) ? trim($inputData['nik']) : '';

// If NIK is not provided in input, check session as fallback
if (empty($nik) && isset($_SESSION['nik'])) {
    $nik = $_SESSION['nik'];
}

// Debug logging
error_log(
    "save_patient_data.php - NIK from input: " .
    (isset($inputData['nik']) ? $inputData['nik'] : 'not set')
);
error_log(
    "save_patient_data.php - NIK from session: " .
    (isset($_SESSION['nik']) ? $_SESSION['nik'] : 'not set')
);
error_log("save_patient_data.php - Final NIK: " . $nik);

// If still no NIK, return error
if (empty($nik)) {
    echo json_encode(
        [
        'success' => false,
        'message' => 'NIK not found in session or request data'
        ]
    );
    exit;
}

// Sanitize all input data
$sanitizedInput = sanitizeInputArray($inputData);

// Get patient data from request with sanitized values
// Support both API field names and form field names for compatibility
$formData = [
    'nik' => $nik,
    'name' => $sanitizedInput['name'] ?? '',
    'date_of_birth' => $sanitizedInput['date_of_birth'] ??
        $sanitizedInput['birth_date'] ?? '',
    'place_of_birth' => $sanitizedInput['place_of_birth'] ??
        $sanitizedInput['birth_place'] ?? '',
    'gender' => $sanitizedInput['gender'] ?? '',
    'education_id' => $sanitizedInput['education_id'] ??
        ($sanitizedInput['education']['id'] ?? null),
    'profession_id' => $sanitizedInput['profession_id'] ??
        ($sanitizedInput['profession']['id'] ?? null),
    'marital_id' => $sanitizedInput['marital_id'] ??
        ($sanitizedInput['marital']['id'] ?? null),
    'religion_id' => $sanitizedInput['religion_id'] ??
        ($sanitizedInput['religion']['id'] ?? null),
    'address' => $sanitizedInput['address'] ?? '',
    'village_id' => $sanitizedInput['village_id'] ??
        ($sanitizedInput['village']['id'] ?? null),
    'phone' => $sanitizedInput['phone'] ?? '',
    'email' => $sanitizedInput['email'] ?? '',
    'job' => $sanitizedInput['job'] ?? '',
    'current_address' => $sanitizedInput['current_address'] ?? '',
    'is_same_address' => (bool)($sanitizedInput['is_same_address'] ?? false),
    'passport' => $sanitizedInput['passport'] ?? '',
    'kitas' => $sanitizedInput['kitas'] ?? '',
    'is_active' => (bool)($sanitizedInput['is_active'] ?? true),
    'institution' => $sanitizedInput['institution'] ?? '',
    'current_village_id' => $sanitizedInput['current_village_id'] ??
        ($sanitizedInput['current_village']['id'] ?? null),
    'person_in_charge' => $sanitizedInput['person_in_charge'] ?? ''
];

// Validate required fields (use correct field names)
$requiredFields = ['name', 'date_of_birth', 'place_of_birth', 'gender', 'address', 'phone'];
$missingFields = validateRequiredFields($formData, $requiredFields);

if (!empty($missingFields)) {
    echo json_encode(
        [
        'success' => false,
        'message' => 'Missing required fields',
        'missing_fields' => $missingFields
        ]
    );
    exit;
}

// Validate email if provided
if (!empty($formData['email']) && !validateEmail($formData['email'])) {
    echo json_encode(
        [
        'success' => false,
        'message' => 'Invalid email format'
        ]
    );
    exit;
}

// Validate phone
if (!validatePhone($formData['phone'])) {
    echo json_encode(
        [
        'success' => false,
        'message' => 'Invalid phone number format'
        ]
    );
    exit;
}

// Validate birth date
if (!validateDate($formData['date_of_birth'])) {
    echo json_encode(
        [
        'success' => false,
        'message' => 'Invalid birth date format (should be YYYY-MM-DD)'
        ]
    );
    exit;
}

// Validate required IDs
$requiredIds = ['religion_id', 'village_id', 'marital_id', 'education_id', 'profession_id'];
foreach ($requiredIds as $idField) {
    if (empty($formData[$idField]) || !is_numeric($formData[$idField])) {
        echo json_encode(
            [
            'success' => false,
            'message' => "Missing or invalid {$idField}",
            'debug_data' => $formData
            ]
        );
        exit;
    }
}

// Validate gender ID (can be string or nested object)
$genderId = null;
if (is_array($formData['gender']) && isset($formData['gender']['id'])) {
    $genderId = (int)$formData['gender']['id'];
} elseif (is_numeric($formData['gender'])) {
    $genderId = (int)$formData['gender'];
}

if (empty($genderId)) {
    echo json_encode(
        [
        'success' => false,
        'message' => 'Missing or invalid gender ID',
        'debug_data' => $formData
        ]
    );
    exit;
}

// Format data for SimKlinik API (match the working Postman example exactly)
$apiData = [
    'name' => $formData['name'],
    'nik' => $formData['nik'],
    'date_of_birth' => $formData['date_of_birth'],
    'place_of_birth' => $formData['place_of_birth'],
    'phone' => $formData['phone'],
    'job' => $formData['job'],
    'religion' => [
        'id' => (int)$formData['religion_id']
    ],
    'address' => $formData['address'],
    'current_address' => $formData['current_address'] ?: $formData['address'],
    'is_same_address' => $formData['is_same_address'],
    'person_in_charge' => $formData['person_in_charge'],
    'gender' => [
        'id' => $genderId
    ],
    'village' => [
        'id' => (int)$formData['village_id']
    ],
    'current_village' => [
        // Use current_village_id if it exists, otherwise use village_id
        'id' => (int)(!empty($formData['current_village_id']) ?
            $formData['current_village_id'] :
            $formData['village_id'])
    ],
    'marital' => [
        'id' => (int)$formData['marital_id']
    ],
    'education' => [
        'id' => (int)$formData['education_id']
    ],
    'profession' => [
        'id' => (int)$formData['profession_id']
    ],
    'passport' => $formData['passport'],
    'kitas' => $formData['kitas'],
    'is_active' => $formData['is_active'],
    'institution' => $formData['institution']
];

// Debug logging - log the data being sent to API
error_log("save_patient_data.php - Sending API data: " . json_encode($apiData));


// Send data to SimKlinik API
$apiResult = createPerson($apiData);
 
// Debug logging to see what's happening
error_log("save_patient_data.php - API Result: " . print_r($apiResult, true));

// Determine the final result based on API response only
if ($apiResult['success']) {
    $result = [
        'success' => true,
        'message' => 'Patient data saved successfully',
        'data' => $apiResult['data']
    ];

    // Store patient data in session
    $_SESSION['patient_data'] = $apiResult['data'];
} else {
    $result = [
        'success' => false,
        'message' => 'Failed to save patient data: ' . ($apiResult['error'] ?? 'Unknown error'),
        'data' => null,
        'api_error' => $apiResult['error']
    ];
}

// Return result
echo json_encode($result);
