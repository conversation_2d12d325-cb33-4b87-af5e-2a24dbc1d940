<?php
/**
 * Debug script for testing get_booking_by_nik functionality
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Testing get_booking_by_nik functionality</h2>";

// Test 1: Check if includes work
echo "<h3>Test 1: Checking includes</h3>";

// Get the current directory and build absolute paths
$currentDir = __DIR__;
$registrationDir = dirname($currentDir);

echo "Current directory: $currentDir<br>";
echo "Registration directory: $registrationDir<br>";

$constantsPath = $registrationDir . '/config/constants.php';
$functionsPath = $registrationDir . '/includes/functions.php';
$bookingFunctionsPath = $registrationDir . '/modules/booking/functions.php';

echo "Constants path: $constantsPath<br>";
echo "Functions path: $functionsPath<br>";
echo "Booking functions path: $bookingFunctionsPath<br>";

try {
    if (file_exists($constantsPath)) {
        require_once $constantsPath;
        echo "✓ constants.php loaded successfully<br>";
    } else {
        echo "✗ constants.php file not found at: $constantsPath<br>";
    }
} catch (Exception $e) {
    echo "✗ Error loading constants.php: " . $e->getMessage() . "<br>";
}

try {
    if (file_exists($functionsPath)) {
        require_once $functionsPath;
        echo "✓ functions.php loaded successfully<br>";
    } else {
        echo "✗ functions.php file not found at: $functionsPath<br>";
    }
} catch (Exception $e) {
    echo "✗ Error loading functions.php: " . $e->getMessage() . "<br>";
}

try {
    if (file_exists($bookingFunctionsPath)) {
        require_once $bookingFunctionsPath;
        echo "✓ booking/functions.php loaded successfully<br>";
    } else {
        echo "✗ booking/functions.php file not found at: $bookingFunctionsPath<br>";
    }
} catch (Exception $e) {
    echo "✗ Error loading booking/functions.php: " . $e->getMessage() . "<br>";
}

// Test 2: Check if API functions are available
echo "<h3>Test 2: Checking API functions</h3>";
if (function_exists('fetchPatientRegistrationsByNIK')) {
    echo "✓ fetchPatientRegistrationsByNIK function exists<br>";
} else {
    echo "✗ fetchPatientRegistrationsByNIK function not found<br>";
}

if (function_exists('getBookingByNIK')) {
    echo "✓ getBookingByNIK function exists<br>";
} else {
    echo "✗ getBookingByNIK function not found<br>";
}

// Test 3: Test API call directly
echo "<h3>Test 3: Testing API call directly</h3>";
$testNIK = '3302194812050002'; // Use a test NIK
echo "Testing with NIK: $testNIK<br>";

if (function_exists('fetchPatientRegistrationsByNIK')) {
    $apiResult = fetchPatientRegistrationsByNIK($testNIK);
    echo "<strong>API Result:</strong><br>";
    echo "<pre>" . print_r($apiResult, true) . "</pre>";
} else {
    echo "Cannot test API - function not available<br>";
}

// Test 4: Test getBookingByNIK function
echo "<h3>Test 4: Testing getBookingByNIK function</h3>";
if (function_exists('getBookingByNIK')) {
    $bookingResult = getBookingByNIK($testNIK);
    echo "<strong>Booking Result:</strong><br>";
    echo "<pre>" . print_r($bookingResult, true) . "</pre>";
} else {
    echo "Cannot test getBookingByNIK - function not available<br>";
}

// Test 5: Check if we can make a simple API request
echo "<h3>Test 5: Testing basic API connectivity</h3>";
if (function_exists('makeApiRequest')) {
    $testUrl = "https://staging.simklinik.com/api/integration/patient/$testNIK/registration";
    echo "Testing URL: $testUrl<br>";
    $basicResult = makeApiRequest($testUrl, 'GET');
    echo "<strong>Basic API Result:</strong><br>";
    echo "<pre>" . print_r($basicResult, true) . "</pre>";
} else {
    echo "makeApiRequest function not available<br>";
}

echo "<h3>Debug Complete</h3>";
?>
