<?php
/**
 * Ticket Template
 *
 * This template displays the ticket for a booking.
 */

// Include booking functions for API data fetching
require_once __DIR__ . '/../modules/booking/functions.php';

// Initialize variables
$bookingNumber = '';
$queueNumber = '';
$patientName = '';
$patientNIK = '';
$poliName = '';
$doctorName = '';
$bookingDate = '';
$bookingTime = '';
$bookingStatus = '';

// Try to get booking data from various sources
$bookingData = null;

// Priority 1: Check if booking data is passed directly
if (isset($bookingData) && is_array($bookingData)) {
    // Data already provided
}
// Priority 2: Check session data
elseif (isset($_SESSION['booking_data'])) {
    $bookingData = $_SESSION['booking_data'];
}
// Priority 3: Try to fetch from API using NIK
elseif (isset($_GET['nik']) && !empty($_GET['nik'])) {
    $nik = trim($_GET['nik']);
    $bookingNumber = isset($_GET['booking']) ? trim($_GET['booking']) : null;

    // Fetch booking data from API
    $apiBookingData = getSingleBookingByNIK($nik, $bookingNumber);
    if ($apiBookingData) {
        $bookingData = $apiBookingData;
    }
}
// Priority 4: Try to get NIK from session and fetch from API
elseif (isset($_SESSION['patient_data']['nik'])) {
    $nik = $_SESSION['patient_data']['nik'];
    $apiBookingData = getSingleBookingByNIK($nik);
    if ($apiBookingData) {
        $bookingData = $apiBookingData;
    }
}

// Extract booking information if we have data
if ($bookingData && is_array($bookingData)) {
    $bookingNumber = $bookingData['booking_number'] ?? '';
    $queueNumber = $bookingData['queue_number'] ?? '';
    $patientName = $bookingData['patient_name'] ?? '';
    $patientNIK = $bookingData['nik'] ?? $bookingData['patient_nik'] ?? '';
    $poliName = $bookingData['poli_name'] ?? '';
    $doctorName = $bookingData['doctor_name'] ?? '';
    $bookingDate = $bookingData['booking_date'] ?? '';
    $bookingTime = $bookingData['booking_time'] ?? '';
    $bookingStatus = $bookingData['status'] ?? 'confirmed';
}

// If patient data is provided separately, use it to fill missing info
if (isset($patientData) && is_array($patientData)) {
    $patientName = $patientData['name'] ?? $patientName;
    $patientNIK = $patientData['nik'] ?? $patientNIK;
}

// Format the booking date to Indonesian format if available
if (!empty($bookingDate)) {
    $dateObj = DateTime::createFromFormat('Y-m-d', $bookingDate);
    if ($dateObj) {
        $months = [
            1 => 'Jan', 2 => 'Feb', 3 => 'Mar', 4 => 'Apr', 5 => 'Mei', 6 => 'Jun',
            7 => 'Jul', 8 => 'Agu', 9 => 'Sep', 10 => 'Okt', 11 => 'Nov', 12 => 'Des'
        ];
        $bookingDate = $dateObj->format('d') . ' ' . $months[(int)$dateObj->format('n')] . ', ' . $dateObj->format('Y');
    }
}

// Generate QR code URL for verification
$baseUrl = 'http://periksa-dokter.local'; // Use local development URL
$qrCodeURL = $baseUrl . '/registration/caribooking.php?nik=' . urlencode($patientNIK) . '&booking=' . urlencode($bookingNumber);
?>
<div class="registration-step">
    <p class="text-center">Klinik Utama PMI Banyumas</p>
    <p class="text-center">Tahap 4 - Tiket</p>

    <?php if (empty($bookingNumber)): ?>
        <!-- No booking data found -->
        <div class="card">
            <div class="card-header text-center bg-warning">
                <h4 class="text-center">Data Booking Tidak Ditemukan</h4>
            </div>
            <div class="card-body text-center">
                <p>Tidak dapat menemukan data booking. Silakan:</p>
                <ul class="list-unstyled">
                    <li>• Periksa kembali NIK yang dimasukkan</li>
                    <li>• Pastikan booking sudah berhasil dibuat</li>
                    <li>• Hubungi petugas jika masalah berlanjut</li>
                </ul>
                <a href="registration.php" class="btn btn-primary">Kembali ke Menu Utama</a>
            </div>
        </div>
    <?php else: ?>
        <!-- Booking data found, show ticket -->
        <div class="card" id="ticket-card">
            <div class="card-header text-center">
                <h4 class="text-center">Tiket Reservasi</h4>
                <?php if (!empty($bookingStatus)): ?>
                    <span class="badge badge-<?php echo $bookingStatus === 'confirmed' ? 'success' : 'warning'; ?>">
                        Status: <?php echo ucfirst($bookingStatus); ?>
                    </span>
                <?php endif; ?>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <table class="table table-borderless">
                            <tr>
                                <th>No. Reservasi</th>
                                <td>:</td>
                                <td id="ticketNumber"><strong><?php echo htmlspecialchars($bookingNumber); ?></strong></td>
                            </tr>
                            <tr>
                                <th>Nama Pasien</th>
                                <td>:</td>
                                <td id="patientName"><?php echo htmlspecialchars($patientName); ?></td>
                            </tr>
                            <tr>
                                <th>NIK</th>
                                <td>:</td>
                                <td id="patientNIK"><?php echo htmlspecialchars($patientNIK); ?></td>
                            </tr>
                            <tr>
                                <th>Poli</th>
                                <td>:</td>
                                <td id="poliName"><?php echo htmlspecialchars($poliName); ?></td>
                            </tr>
                            <tr>
                                <th>Dokter</th>
                                <td>:</td>
                                <td id="doctorName"><?php echo htmlspecialchars($doctorName); ?></td>
                            </tr>
                            <tr>
                                <th>Tanggal Kunjungan</th>
                                <td>:</td>
                                <td id="bookingDate"><?php echo htmlspecialchars($bookingDate); ?></td>
                            </tr>
                            <tr>
                                <th>Jam</th>
                                <td>:</td>
                                <td id="bookingTime"><?php echo htmlspecialchars($bookingTime); ?></td>
                            </tr>
                            <tr>
                                <th>No. Antrian</th>
                                <td>:</td>
                                <td id="queueNumber"><strong><?php echo htmlspecialchars($queueNumber); ?></strong></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-4 text-center">
                        <div id="qrcode" style="margin: 20px auto; padding: 20px; border: 2px dashed #007bff; border-radius: 10px; background: #f8f9fa; min-height: 170px; display: flex; align-items: center; justify-content: center;">
                            <div style="color: #6c757d; font-size: 14px;">Generating QR Code...</div>
                        </div>
                        <p style="margin-top: 10px; color: #666; font-size: 12px;">Scan untuk verifikasi booking</p>
                    </div>
                </div>
                <div class="text-center mt-4">
                    <button id="download-ticket" class="btn btn-primary">
                        <i class="fas fa-download"></i> Download Tiket
                    </button>
                    <a href="registration.php" class="btn btn-secondary">
                        <i class="fas fa-home"></i> Kembali ke Menu Utama
                    </a>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- QR Code and PDF Generation Scripts -->
<script src="https://cdn.jsdelivr.net/npm/qrcode-generator@1.4.4/qrcode.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/qrcodejs/1.0.0/qrcode.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>

<script>
// Booking data for JavaScript
var bookingData = {
    booking_number: '<?php echo $bookingNumber; ?>',
    queue_number: '<?php echo $queueNumber; ?>',
    patient_name: '<?php echo $patientName; ?>',
    patient_nik: '<?php echo $patientNIK; ?>',
    poli_name: '<?php echo $poliName; ?>',
    doctor_name: '<?php echo $doctorName; ?>',
    booking_date: '<?php echo $bookingDate; ?>',
    booking_time: '<?php echo $bookingTime; ?>'
};

var qrCodeURL = '<?php echo $qrCodeURL; ?>';

// Generate QR Code when page loads
document.addEventListener('DOMContentLoaded', function() {
    generateTicketQRCode();
    setupDownloadButton();
});

function generateTicketQRCode() {
    const qrCodeElement = document.getElementById('qrcode');

    if (!qrCodeElement) {
        console.error('QR code element not found');
        return;
    }

    // Clear any existing content
    qrCodeElement.innerHTML = '';

    // Check if we have booking data
    if (!bookingData.booking_number) {
        console.error('No booking number available for QR code');
        qrCodeElement.innerHTML = '<p style="color: #999; font-size: 12px;">QR Code tidak tersedia</p>';
        return;
    }

    try {
        // Method 1: Try using QRCode library (qrcodejs)
        if (typeof QRCode !== 'undefined') {
            console.log('Using QRCode library');
            new QRCode(qrCodeElement, {
                text: qrCodeURL,
                width: 150,
                height: 150,
                colorDark: "#000000",
                colorLight: "#ffffff",
                correctLevel: QRCode.CorrectLevel.H
            });
        }
        // Method 2: Try using qrcode-generator library
        else if (typeof qrcode !== 'undefined') {
            console.log('Using qrcode-generator library');
            const qr = qrcode(0, 'M');
            qr.addData(qrCodeURL);
            qr.make();
            qrCodeElement.innerHTML = qr.createImgTag(5);
        }
        // Method 3: Fallback to external QR code service
        else {
            console.log('Using fallback QR code service');
            const qrImg = document.createElement('img');
            qrImg.src = 'https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=' + encodeURIComponent(qrCodeURL);
            qrImg.alt = 'QR Code';
            qrImg.style.maxWidth = '150px';
            qrImg.style.maxHeight = '150px';
            qrCodeElement.appendChild(qrImg);
        }

        console.log('QR Code generated successfully with URL:', qrCodeURL);
    } catch (error) {
        console.error('Error generating QR code:', error);
        qrCodeElement.innerHTML = '<p style="color: #999; font-size: 12px;">Error generating QR Code</p>';
    }
}

function setupDownloadButton() {
    const downloadButton = document.getElementById('download-ticket');
    if (downloadButton) {
        // Mark that this button has a handler attached
        downloadButton.setAttribute('data-pdf-handler-attached', 'true');
        downloadButton.addEventListener('click', generatePDF);
    }
}

function generatePDF() {
    // Check if html2canvas is available
    if (typeof html2canvas !== 'function') {
        console.error('html2canvas not available');
        alert('PDF generation library (html2canvas) not available. Please try again later.');
        return;
    }

    // Check if jsPDF is available (UMD version check)
    if (typeof window.jspdf === 'undefined' && typeof jsPDF === 'undefined') {
        console.error('jsPDF not available');
        alert('PDF generation library (jsPDF) not available. Please try again later.');
        return;
    }

    // Get the ticket card element
    const ticketCard = document.getElementById('ticket-card');
    if (!ticketCard) {
        console.error('Ticket card element not found');
        alert('Ticket element not found.');
        return;
    }

    // Show loading message
    const downloadButton = document.getElementById('download-ticket');
    const originalText = downloadButton ? downloadButton.innerHTML : '';
    if (downloadButton) {
        downloadButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generating PDF...';
        downloadButton.disabled = true;
    }

    // Use html2canvas to convert the ticket card to an image
    html2canvas(ticketCard, {
        scale: 2,
        useCORS: true,
        allowTaint: true
    }).then(canvas => {
        try {
            // Add the image to the PDF
            const imgData = canvas.toDataURL('image/png');

            // Get jsPDF reference (handle both UMD and direct versions)
            const { jsPDF } = window.jspdf || window;
            const doc = new (jsPDF || window.jsPDF)();

            const imgWidth = 190;
            const imgHeight = canvas.height * imgWidth / canvas.width;

            doc.addImage(imgData, 'PNG', 10, 10, imgWidth, imgHeight);

            // Generate filename
            const filename = bookingData.booking_number ?
                'tiket-' + bookingData.booking_number + '.pdf' : 'tiket-reservasi.pdf';

            // Save the PDF
            doc.save(filename);

            console.log('PDF generated successfully');
            // Show success message briefly
            if (downloadButton) {
                downloadButton.innerHTML = '<i class="fas fa-check"></i> Downloaded!';
                setTimeout(() => {
                    downloadButton.innerHTML = originalText;
                    downloadButton.disabled = false;
                }, 2000);
            }
        } catch (error) {
            console.error('Error generating PDF:', error);
            alert('Error generating PDF: ' + error.message);
            // Restore button
            if (downloadButton) {
                downloadButton.innerHTML = originalText;
                downloadButton.disabled = false;
            }
        }
    }).catch(error => {
        console.error('Error capturing ticket:', error);
        alert('Error capturing ticket: ' + error.message);
        // Restore button
        if (downloadButton) {
            downloadButton.innerHTML = originalText;
            downloadButton.disabled = false;
        }
    });
}
</script>