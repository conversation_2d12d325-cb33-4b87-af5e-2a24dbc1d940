<?php
/**
 * Get Booking by Code API
 *
 * This API retrieves booking data by booking code or NIK.
 */

// Start session
session_start();

// Include configuration files 
// require_once '../config/constants.php';

// Include utility functions
// require_once '../includes/functions.php';

// Include booking functions
require_once '../modules/booking/functions.php'; 
// print('<pre>'.print_r($_GET['nik'], true).'</pre>');
die;
// Set content type to JSON
header('Content-Type: application/json');

// Check if request method is GET
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    echo json_encode(
        [
        'success' => false,
        'message' => 'Invalid request method'
        ]
    );
    exit;
}

// Get search term from request
$NIK = isset($_GET['nik']) ? trim($_GET['nik']) : '';
print('<pre>'.print_r($NIK, true).'</pre>');
die;
// Debug: Log NIK to error log instead of printing to output
error_log("DEBUG - NIK received: " . $NIK);
error_log("DEBUG - GET data: " . print_r($_GET, true));

if (empty($NIK)) {
    echo json_encode(
        [
        'success' => false,
        'message' => 'Silakan masukkan NIK'
        ]
    );
    exit;
}

// Try to get booking by NIK (API only)
$result = getBookingByNIK($NIK);
// print('<pre>'.print_r($result,true).'</pre>');
// die;
if ($result) {
    echo json_encode(
        [
        'success' => true,
        'message' => 'Booking found',
        'is_multiple' => $result['is_multiple'],
        'data' => $result['bookings']
        ]
    );
} else {
    echo json_encode(
        [
        'success' => false,
        'message' => 'Booking tidak ditemukan. Silakan periksa kembali Kode Booking atau NIK yang dimasukkan.'
        ]
    );
}
